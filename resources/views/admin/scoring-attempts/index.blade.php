@extends('layouts.admin')

@section('title', 'Quản Lý Bài Thi - Admin')

@section('content')
<div class="modern-scoring-management">
    <!-- Hero Header -->
    <div class="hero-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="hero-content">
                        <div class="hero-badge">
                            <i class="fas fa-file-alt"></i>
                            <span>QUẢN LÝ BÀI THI</span>
                        </div>
                        <h1 class="hero-title">
                            Hệ Thống Quản Lý Bài Thi IELTS
                        </h1>
                        <p class="hero-subtitle">
                            <PERSON>, phân tích và quản lý tất cả bài thi trong hệ thống một cách hiệu quả
                        </p>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="hero-actions">
                        <button type="button" class="btn-modern btn-filter" onclick="toggleAdvancedFilter()">
                            <i class="fas fa-sliders-h"></i>
                            <span>Bộ lọc nâng cao</span>
                        </button>
                        <a href="{{ route('admin.scoring-attempts.export', request()->query()) }}" class="btn-modern btn-export">
                            <i class="fas fa-download"></i>
                            <span>Xuất Excel</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Statistics Dashboard -->
    <div class="container-fluid">
        <div class="stats-dashboard">
            <div class="row g-4">
                <div class="col-xl-3 col-md-6">
                    <div class="modern-stat-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="stat-card-inner">
                            <div class="stat-icon-wrapper">
                                <div class="stat-icon bg-gradient-primary">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="stat-glow primary"></div>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" data-target="{{ $stats['total'] }}">0</div>
                                <div class="stat-label">Tổng số bài thi</div>
                                <div class="stat-trend positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+{{ $stats['this_week'] }} tuần này</span>
                                </div>
                            </div>
                            <div class="stat-chart">
                                <div class="mini-chart" data-chart="total"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="modern-stat-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="stat-card-inner">
                            <div class="stat-icon-wrapper">
                                <div class="stat-icon bg-gradient-success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-glow success"></div>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" data-target="{{ $stats['completed'] }}">0</div>
                                <div class="stat-label">Đã hoàn thành</div>
                                <div class="stat-trend positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>{{ round(($stats['completed']/$stats['total'])*100, 1) }}% tổng số</span>
                                </div>
                            </div>
                            <div class="stat-chart">
                                <div class="mini-chart" data-chart="completed"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="modern-stat-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="stat-card-inner">
                            <div class="stat-icon-wrapper">
                                <div class="stat-icon bg-gradient-warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-glow warning"></div>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" data-target="{{ $stats['pending'] }}">0</div>
                                <div class="stat-label">Đang xử lý</div>
                                <div class="stat-trend {{ $stats['pending'] > 0 ? 'warning' : 'positive' }}">
                                    <i class="fas fa-{{ $stats['pending'] > 0 ? 'exclamation-triangle' : 'check' }}"></i>
                                    <span>{{ $stats['pending'] > 0 ? 'Cần xử lý' : 'Tất cả đã xong' }}</span>
                                </div>
                            </div>
                            <div class="stat-chart">
                                <div class="mini-chart" data-chart="pending"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="modern-stat-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="stat-card-inner">
                            <div class="stat-icon-wrapper">
                                <div class="stat-icon bg-gradient-info">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div class="stat-glow info"></div>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" data-target="{{ $stats['today'] }}">0</div>
                                <div class="stat-label">Hôm nay</div>
                                <div class="stat-trend positive">
                                    <i class="fas fa-calendar"></i>
                                    <span>{{ now()->format('d/m/Y') }}</span>
                                </div>
                            </div>
                            <div class="stat-chart">
                                <div class="mini-chart" data-chart="today"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Advanced Filter Panel -->
    <div class="container-fluid">
        <div class="advanced-filter-panel" id="advancedFilter" style="display: none;">
            <div class="filter-card">
                <div class="filter-header">
                    <h3 class="filter-title">
                        <i class="fas fa-sliders-h"></i>
                        Bộ lọc nâng cao
                    </h3>
                    <button type="button" class="btn-close-filter" onclick="toggleAdvancedFilter()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form method="GET" action="{{ route('admin.scoring-attempts.index') }}" class="filter-form">
                    <div class="filter-grid">
                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-search"></i>
                                Tìm kiếm người dùng
                            </label>
                            <div class="filter-input-wrapper">
                                <input type="text"
                                       class="filter-input"
                                       name="search"
                                       value="{{ request('search') }}"
                                       placeholder="Nhập tên hoặc email...">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-flag"></i>
                                Trạng thái
                            </label>
                            <div class="filter-select-wrapper">
                                <select class="filter-select" name="status">
                                    <option value="">Tất cả trạng thái</option>
                                    @foreach($statuses as $status)
                                        <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                            {{ ucfirst($status) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-tasks"></i>
                                Loại bài thi
                            </label>
                            <div class="filter-select-wrapper">
                                <select class="filter-select" name="task_type">
                                    <option value="">Tất cả loại</option>
                                    @foreach($taskTypes as $type)
                                        <option value="{{ $type }}" {{ request('task_type') == $type ? 'selected' : '' }}>
                                            {{ ucfirst(str_replace('_', ' ', $type)) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-calendar-alt"></i>
                                Từ ngày
                            </label>
                            <div class="filter-input-wrapper">
                                <input type="date"
                                       class="filter-input"
                                       name="date_from"
                                       value="{{ request('date_from') }}">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-calendar-check"></i>
                                Đến ngày
                            </label>
                            <div class="filter-input-wrapper">
                                <input type="date"
                                       class="filter-input"
                                       name="date_to"
                                       value="{{ request('date_to') }}">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-user"></i>
                                Người dùng cụ thể
                            </label>
                            <div class="filter-select-wrapper">
                                <select class="filter-select" name="user_id">
                                    <option value="">Tất cả người dùng</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }} ({{ $user->email }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="filter-actions">
                        <button type="submit" class="btn-filter-apply">
                            <i class="fas fa-filter"></i>
                            Áp dụng bộ lọc
                        </button>
                        @if(request()->hasAny(['search', 'status', 'task_type', 'date_from', 'date_to', 'user_id']))
                            <a href="{{ route('admin.scoring-attempts.index') }}" class="btn-filter-clear">
                                <i class="fas fa-times"></i>
                                Xóa bộ lọc
                            </a>
                        @endif
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modern Data Table -->
    <div class="container-fluid">
        <div class="modern-table-container">
            <div class="table-header">
                <div class="table-title-section">
                    <h3 class="table-title">
                        <i class="fas fa-list"></i>
                        Danh sách bài thi
                    </h3>
                    <div class="table-subtitle">
                        <span class="result-count">{{ $attempts->total() }} kết quả</span>
                        @if(request()->hasAny(['search', 'status', 'task_type', 'date_from', 'date_to', 'user_id']))
                            <span class="filter-indicator">
                                <i class="fas fa-filter"></i>
                                Đã lọc
                            </span>
                        @endif
                    </div>
                </div>
                @if($attempts->count() > 0)
                    <div class="table-actions">
                        <button type="button" class="btn-bulk-action" onclick="bulkDelete()" id="bulkDeleteBtn" style="display: none;">
                            <i class="fas fa-trash"></i>
                            <span>Xóa đã chọn</span>
                        </button>
                        <div class="view-toggle">
                            <button type="button" class="view-btn active" data-view="table">
                                <i class="fas fa-table"></i>
                            </button>
                            <button type="button" class="view-btn" data-view="grid">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>
                    </div>
                @endif
            </div>

            <div class="table-content" id="tableView">
            @if($attempts->count() > 0)
                <div class="modern-table-wrapper">
                    <div class="table-scroll-container">
                        <table class="modern-table" id="modernDataTable">
                            <thead class="table-head">
                                <tr>
                                    <th class="select-column">
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="selectAll" class="modern-checkbox">
                                            <label for="selectAll" class="checkbox-label"></label>
                                        </div>
                                    </th>
                                    <th class="user-column">
                                        <div class="th-content">
                                            <i class="fas fa-user"></i>
                                            <span>Người dùng</span>
                                        </div>
                                    </th>
                                    <th class="type-column">
                                        <div class="th-content">
                                            <i class="fas fa-tasks"></i>
                                            <span>Loại bài thi</span>
                                        </div>
                                    </th>
                                    <th class="question-column">
                                        <div class="th-content">
                                            <i class="fas fa-question-circle"></i>
                                            <span>Câu hỏi</span>
                                        </div>
                                    </th>
                                    <th class="score-column">
                                        <div class="th-content">
                                            <i class="fas fa-star"></i>
                                            <span>Điểm số</span>
                                        </div>
                                    </th>
                                    <th class="words-column">
                                        <div class="th-content">
                                            <i class="fas fa-file-word"></i>
                                            <span>Số từ</span>
                                        </div>
                                    </th>
                                    <th class="status-column">
                                        <div class="th-content">
                                            <i class="fas fa-flag"></i>
                                            <span>Trạng thái</span>
                                        </div>
                                    </th>
                                    <th class="date-column">
                                        <div class="th-content">
                                            <i class="fas fa-calendar"></i>
                                            <span>Ngày tạo</span>
                                        </div>
                                    </th>
                                    <th class="actions-column">
                                        <div class="th-content">
                                            <i class="fas fa-cogs"></i>
                                            <span>Hành động</span>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="table-body">
                                @foreach($attempts as $index => $attempt)
                                    <tr class="table-row" data-aos="fade-up" data-aos-delay="{{ 100 + ($index * 50) }}">
                                        <td class="select-cell">
                                            <div class="checkbox-wrapper">
                                                <input type="checkbox"
                                                       name="attempt_ids[]"
                                                       value="{{ $attempt->id }}"
                                                       class="modern-checkbox attempt-checkbox"
                                                       id="attempt_{{ $attempt->id }}">
                                                <label for="attempt_{{ $attempt->id }}" class="checkbox-label"></label>
                                            </div>
                                        </td>
                                        <td class="user-cell">
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <div class="avatar-circle {{ ($attempt->user->role ?? 'user') === 'admin' ? 'admin' : 'user' }}">
                                                        {{ substr($attempt->user->name, 0, 2) }}
                                                    </div>
                                                    <div class="user-status {{ ($attempt->user->is_active ?? true) ? 'online' : 'offline' }}"></div>
                                                </div>
                                                <div class="user-details">
                                                    <div class="user-name">{{ $attempt->user->name }}</div>
                                                    <div class="user-email">{{ $attempt->user->email }}</div>
                                                    <div class="user-meta">
                                                        <span class="role-badge {{ $attempt->user->role ?? 'user' }}">
                                                            {{ ucfirst($attempt->user->role ?? 'user') }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="type-cell">
                                            <div class="task-type-badge {{ str_replace('_', '-', $attempt->task_type) }}">
                                                <div class="badge-icon">
                                                    <i class="fas fa-{{ $attempt->task_type === 'task2' ? 'edit' : 'chart-line' }}"></i>
                                                </div>
                                                <div class="badge-text">
                                                    {{ ucfirst(str_replace('_', ' ', $attempt->task_type)) }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="question-cell">
                                            <div class="question-preview">
                                                <div class="question-title">
                                                    {{ $attempt->essayQuestion ? $attempt->essayQuestion->title : 'Custom Question' }}
                                                </div>
                                                @if($attempt->essayQuestion && $attempt->essayQuestion->description)
                                                    <div class="question-description">
                                                        {{ Str::limit($attempt->essayQuestion->description, 100) }}
                                                    </div>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="score-cell">
                                            @if($attempt->isCompleted())
                                                <div class="score-display">
                                                    <div class="score-circle-mini">
                                                        <div class="score-number">{{ $attempt->overall_band_score }}</div>
                                                    </div>
                                                    <div class="score-breakdown">
                                                        <div class="score-label">Band Score</div>
                                                        <div class="score-details">
                                                            <span class="score-item">TA: {{ $attempt->task_achievement ?? '-' }}</span>
                                                            <span class="score-item">CC: {{ $attempt->coherence_cohesion ?? '-' }}</span>
                                                            <span class="score-item">LR: {{ $attempt->lexical_resource ?? '-' }}</span>
                                                            <span class="score-item">GRA: {{ $attempt->grammar_accuracy ?? '-' }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            @else
                                                <div class="score-pending">
                                                    <i class="fas fa-clock"></i>
                                                    <span>Chưa có điểm</span>
                                                </div>
                                            @endif
                                        </td>
                                        <td class="words-cell">
                                            <div class="word-count-display">
                                                <div class="word-number">{{ $attempt->word_count ?? 0 }}</div>
                                                <div class="word-label">từ</div>
                                                <div class="word-progress">
                                                    @php
                                                        $minWords = $attempt->task_type === 'task2' ? 250 : 150;
                                                        $progress = min(100, (($attempt->word_count ?? 0) / $minWords) * 100);
                                                    @endphp
                                                    <div class="progress-bar" style="width: {{ $progress }}%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="status-cell">
                                            <div class="status-badge status-{{ $attempt->status }}">
                                                <div class="status-icon">
                                                    @if($attempt->status === 'completed')
                                                        <i class="fas fa-check-circle"></i>
                                                    @elseif($attempt->status === 'pending')
                                                        <i class="fas fa-clock"></i>
                                                    @else
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                    @endif
                                                </div>
                                                <div class="status-text">
                                                    @if($attempt->status === 'completed')
                                                        Hoàn thành
                                                    @elseif($attempt->status === 'pending')
                                                        Đang xử lý
                                                    @else
                                                        Lỗi
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td class="date-cell">
                                            <div class="date-display">
                                                <div class="date-main">{{ $attempt->created_at->format('d/m/Y') }}</div>
                                                <div class="date-time">{{ $attempt->created_at->format('H:i') }}</div>
                                                <div class="date-relative">{{ $attempt->created_at->diffForHumans() }}</div>
                                            </div>
                                        </td>
                                        <td class="actions-cell">
                                            <div class="action-buttons">
                                                <a href="{{ route('admin.scoring-attempts.show', $attempt->id) }}"
                                                   class="action-btn view-btn"
                                                   title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button"
                                                        class="action-btn delete-btn"
                                                        onclick="deleteAttempt({{ $attempt->id }})"
                                                        title="Xóa bài thi">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <div class="action-menu">
                                                    <button type="button" class="action-btn menu-btn" onclick="toggleActionMenu({{ $attempt->id }})">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <div class="action-dropdown" id="actionMenu_{{ $attempt->id }}">
                                                        <a href="{{ route('admin.scoring-attempts.show', $attempt->id) }}" class="dropdown-item">
                                                            <i class="fas fa-eye"></i>
                                                            Xem chi tiết
                                                        </a>
                                                        @if($attempt->canBeShared())
                                                            <a href="{{ $attempt->getShareUrl() }}" target="_blank" class="dropdown-item">
                                                                <i class="fas fa-external-link-alt"></i>
                                                                Xem công khai
                                                            </a>
                                                        @endif
                                                        <div class="dropdown-divider"></div>
                                                        <button type="button" class="dropdown-item danger" onclick="deleteAttempt({{ $attempt->id }})">
                                                            <i class="fas fa-trash"></i>
                                                            Xóa bài thi
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Modern Pagination -->
                <div class="modern-pagination-wrapper">
                    <div class="pagination-info">
                        <span class="pagination-text">
                            Hiển thị <strong>{{ $attempts->firstItem() }}</strong> - <strong>{{ $attempts->lastItem() }}</strong>
                            trong tổng số <strong>{{ number_format($attempts->total()) }}</strong> kết quả
                        </span>
                    </div>
                    <div class="pagination-controls">
                        {{ $attempts->appends(request()->query())->links('pagination::bootstrap-4') }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Không có bài thi nào</h5>
                    <p class="text-muted">Chưa có bài thi nào trong hệ thống hoặc không khớp với bộ lọc.</p>
                </div>
            @endif
        </div>
    </div>
</div>
<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa bài thi này không?</p>
                <p class="text-danger"><strong>Lưu ý:</strong> Hành động này không thể hoàn tác!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Form -->
<form id="bulkDeleteForm" method="POST" action="{{ route('admin.scoring-attempts.bulk-delete') }}" style="display: none;">
    @csrf
    <div id="bulkDeleteIds"></div>
</form>

<style>
/* Modern Scoring Management Styles */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);

    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);

    --modern-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    --modern-shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
    --modern-radius: 20px;
    --modern-transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-scoring-management {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 0;
}

/* Hero Header */
.hero-header {
    background: var(--primary-gradient);
    padding: 4rem 0 2rem;
    position: relative;
    overflow: hidden;
    margin: -2rem -2rem 2rem -2rem;
}

.hero-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    letter-spacing: 1px;
    margin-bottom: 1.5rem;
}

.hero-title {
    color: white;
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    line-height: 1.2;
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    font-weight: 300;
    margin-bottom: 0;
    max-width: 600px;
}

.hero-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
}

.btn-modern {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border-radius: 15px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--modern-transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
    backdrop-filter: blur(20px);
}

.btn-filter {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-filter:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    color: white;
}

.btn-export {
    background: rgba(76, 175, 80, 0.9);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-export:hover {
    background: rgba(76, 175, 80, 1);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);
    color: white;
}

/* Modern Statistics Dashboard */
.stats-dashboard {
    padding: 2rem 0;
}

.modern-stat-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--modern-radius);
    padding: 2rem;
    box-shadow: var(--glass-shadow);
    transition: var(--modern-transition);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.modern-stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--modern-shadow-hover);
}

.stat-card-inner {
    position: relative;
    z-index: 2;
}

.stat-icon-wrapper {
    position: relative;
    margin-bottom: 1.5rem;
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: var(--modern-transition);
}

.modern-stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.bg-gradient-primary {
    background: var(--primary-gradient);
}

.bg-gradient-success {
    background: var(--success-gradient);
}

.bg-gradient-warning {
    background: var(--warning-gradient);
}

.bg-gradient-info {
    background: var(--info-gradient);
}

.stat-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border-radius: 30px;
    opacity: 0;
    transition: var(--modern-transition);
    z-index: -1;
}

.stat-glow.primary {
    background: var(--primary-gradient);
}

.stat-glow.success {
    background: var(--success-gradient);
}

.stat-glow.warning {
    background: var(--warning-gradient);
}

.stat-glow.info {
    background: var(--info-gradient);
}

.modern-stat-card:hover .stat-glow {
    opacity: 0.2;
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    color: #1a202c;
    margin-bottom: 0.5rem;
    line-height: 1;
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: #4a5568;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
}

.stat-trend.positive {
    background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
    color: #22543d;
}

.stat-trend.warning {
    background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
    color: #c53030;
}

.mini-chart {
    height: 40px;
    margin-top: 1rem;
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, rgba(102, 126, 234, 0.3) 100%);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.mini-chart::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.5) 50%, transparent 100%);
    animation: chartPulse 2s ease-in-out infinite;
}

@keyframes chartPulse {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

/* Advanced Filter Panel */
.advanced-filter-panel {
    margin: 2rem 0;
    position: relative;
}

.filter-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--modern-radius);
    box-shadow: var(--glass-shadow);
    overflow: hidden;
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: between;
    align-items: center;
}

.filter-title {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.btn-close-filter {
    background: none;
    border: none;
    color: #718096;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--modern-transition);
}

.btn-close-filter:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #2d3748;
}

.filter-form {
    padding: 2rem;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.filter-label {
    color: #4a5568;
    font-weight: 600;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-input-wrapper,
.filter-select-wrapper {
    position: relative;
}

.filter-input,
.filter-select {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: #2d3748;
    font-size: 1rem;
    transition: var(--modern-transition);
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-input::placeholder {
    color: #a0aec0;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-filter-apply {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--modern-transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-filter-apply:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.btn-filter-clear {
    background: transparent;
    color: #718096;
    border: 2px solid rgba(113, 128, 150, 0.3);
    padding: 1rem 2rem;
    border-radius: 15px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--modern-transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-filter-clear:hover {
    background: rgba(113, 128, 150, 0.1);
    border-color: rgba(113, 128, 150, 0.5);
    color: #4a5568;
    text-decoration: none;
}

/* Modern Table Container */
.modern-table-container {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--modern-radius);
    box-shadow: var(--glass-shadow);
    overflow: hidden;
    margin: 2rem 0;
}

.table-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.table-title-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.table-title {
    color: #2d3748;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.table-subtitle {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.result-count {
    color: #718096;
    font-size: 1rem;
    font-weight: 500;
}

.filter-indicator {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-bulk-action {
    background: var(--danger-gradient);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--modern-transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0;
    transform: scale(0.8);
    animation: fadeInScale 0.3s ease-out forwards;
}

@keyframes fadeInScale {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.btn-bulk-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
}

.view-toggle {
    display: flex;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.25rem;
}

.view-btn {
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    color: #718096;
    cursor: pointer;
    transition: var(--modern-transition);
}

.view-btn.active,
.view-btn:hover {
    background: white;
    color: #2d3748;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Modern Table Styles */
.modern-table-wrapper {
    background: transparent;
    border-radius: 0 0 var(--modern-radius) var(--modern-radius);
    overflow: hidden;
}

.table-scroll-container {
    overflow-x: auto;
    max-height: 60vh;
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

.table-scroll-container::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

.table-scroll-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.table-scroll-container::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 4px;
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: transparent;
}

.table-head {
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-head th {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    backdrop-filter: blur(20px);
    border: none;
    padding: 1.5rem 1rem;
    text-align: left;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.th-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #4a5568;
    font-weight: 700;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-row {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--modern-transition);
}

.table-row:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.table-row td {
    padding: 1.5rem 1rem;
    border: none;
    vertical-align: middle;
}

/* Checkbox Styles */
.checkbox-wrapper {
    position: relative;
    display: inline-block;
}

.modern-checkbox {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #cbd5e0;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: var(--modern-transition);
}

.modern-checkbox:checked {
    background: var(--primary-gradient);
    border-color: transparent;
}

.checkbox-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    color: white;
    font-size: 12px;
}

.modern-checkbox:checked + .checkbox-label::after {
    content: '✓';
}

/* User Cell Styles */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    position: relative;
}

.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.avatar-circle.admin {
    background: var(--danger-gradient);
}

.avatar-circle.user {
    background: var(--primary-gradient);
}

.user-status {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.user-status.online {
    background: #48bb78;
}

.user-status.offline {
    background: #a0aec0;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 1rem;
}

.user-email {
    color: #718096;
    font-size: 0.85rem;
}

.user-meta {
    display: flex;
    gap: 0.5rem;
}

.role-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-badge.admin {
    background: rgba(255, 107, 107, 0.2);
    color: #c53030;
}

.role-badge.user {
    background: rgba(102, 126, 234, 0.2);
    color: #667eea;
}

/* Task Type Badge */
.task-type-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.85rem;
    max-width: fit-content;
}

.task-type-badge.task1-academic {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.task-type-badge.task1-general {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.task-type-badge.task2 {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #2d3748;
}

.badge-icon {
    font-size: 1rem;
}

/* Question Preview */
.question-preview {
    max-width: 250px;
}

.question-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.question-description {
    color: #718096;
    font-size: 0.8rem;
    line-height: 1.3;
}

/* Score Display */
.score-display {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.score-circle-mini {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 900;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.score-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.score-label {
    color: #718096;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.score-details {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.score-item {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 600;
}

.score-pending {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #a0aec0;
    font-style: italic;
}

/* Word Count Display */
.word-count-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.word-number {
    font-weight: 700;
    font-size: 1.2rem;
    color: #2d3748;
}

.word-label {
    color: #718096;
    font-size: 0.8rem;
}

.word-progress {
    width: 60px;
    height: 4px;
    background: rgba(203, 213, 224, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--success-gradient);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* Status Badge */
.status-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.85rem;
    max-width: fit-content;
}

.status-badge.status-completed {
    background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
    color: #22543d;
}

.status-badge.status-pending {
    background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
    color: #c53030;
}

.status-badge.status-failed {
    background: linear-gradient(135deg, #fed7d7 0%, #fc8181 100%);
    color: #c53030;
}

.status-icon {
    font-size: 1rem;
}

/* Date Display */
.date-display {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.date-main {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.9rem;
}

.date-time {
    color: #718096;
    font-size: 0.8rem;
}

.date-relative {
    color: #a0aec0;
    font-size: 0.7rem;
    font-style: italic;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--modern-transition);
    text-decoration: none;
    font-size: 0.9rem;
}

.view-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.view-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
    color: white;
}

.delete-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
}

.delete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.menu-btn {
    background: rgba(113, 128, 150, 0.2);
    color: #718096;
}

.menu-btn:hover {
    background: rgba(113, 128, 150, 0.3);
    color: #4a5568;
}

.action-menu {
    position: relative;
}

.action-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    min-width: 200px;
    z-index: 1000;
    display: none;
    overflow: hidden;
}

.action-dropdown.show {
    display: block;
    animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    color: #4a5568;
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: var(--modern-transition);
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background: #f7fafc;
    color: #2d3748;
}

.dropdown-item.danger {
    color: #e53e3e;
}

.dropdown-item.danger:hover {
    background: #fed7d7;
    color: #c53030;
}

.dropdown-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 0.5rem 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .hero-header {
        padding: 2rem 0 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-actions {
        margin-top: 1rem;
    }

    .table-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .table-actions {
        justify-content: space-between;
    }

    .modern-table-wrapper {
        max-height: 60vh;
    }

    .user-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .score-display {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
}

@media (max-width: 576px) {
    .filter-grid {
        grid-template-columns: 1fr;
    }

    .filter-actions {
        flex-direction: column;
    }

    .stats-dashboard .row {
        margin: 0;
    }

    .modern-stat-card {
        margin-bottom: 1rem;
    }
}

/* Animation for number counting */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-number {
    animation: countUp 0.8s ease-out;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modern Pagination Styles */
.modern-pagination-wrapper {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    padding: 2rem;
    border-radius: 0 0 var(--modern-radius) var(--modern-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.pagination-info {
    display: flex;
    align-items: center;
}

.pagination-text {
    color: #4a5568;
    font-size: 0.95rem;
    font-weight: 500;
}

.pagination-text strong {
    color: #2d3748;
    font-weight: 700;
}

.pagination-controls {
    display: flex;
    align-items: center;
}

/* Override Bootstrap pagination styles */
.pagination {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-item {
    margin: 0;
}

.page-link {
    border: none;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    color: #4a5568;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    transition: var(--modern-transition);
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.page-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.page-item.active .page-link {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.page-item.disabled .page-link {
    background: rgba(255, 255, 255, 0.1);
    color: #a0aec0;
    cursor: not-allowed;
}

.page-item.disabled .page-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #a0aec0;
    transform: none;
    box-shadow: none;
}

/* Pagination arrows */
.page-link[aria-label="Previous"],
.page-link[aria-label="Next"] {
    font-size: 1.1rem;
}

/* Responsive pagination */
@media (max-width: 768px) {
    .modern-pagination-wrapper {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }

    .page-link {
        padding: 0.5rem 0.75rem;
        min-width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }
}

/* Loading overlay for table */
.table-loading {
    position: relative;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.table-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 101;
}

/* Enhanced table row animations */
.table-row {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Smooth transitions for all interactive elements */
* {
    transition: var(--modern-transition);
}

/* Focus states for accessibility */
.modern-checkbox:focus,
.filter-input:focus,
.filter-select:focus,
.action-btn:focus,
.page-link:focus {
    outline: 2px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .hero-header,
    .advanced-filter-panel,
    .table-actions,
    .action-buttons,
    .modern-pagination-wrapper {
        display: none !important;
    }

    .modern-table-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .table-scroll-container {
        max-height: none;
        overflow: visible;
    }
}
</style>

<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 600,
        easing: 'ease-out-cubic',
        once: true,
        offset: 50
    });

    // Animate statistics numbers
    animateNumbers();

    // Initialize modern interactions
    initializeModernFeatures();

    // Select All functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const attemptCheckboxes = document.querySelectorAll('.attempt-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            attemptCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            toggleBulkDeleteButton();
        });
    }

    attemptCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.attempt-checkbox:checked').length;
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedCount === attemptCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < attemptCheckboxes.length;
            }
            toggleBulkDeleteButton();
        });
    });

    function toggleBulkDeleteButton() {
        const checkedCount = document.querySelectorAll('.attempt-checkbox:checked').length;
        if (bulkDeleteBtn) {
            if (checkedCount > 0) {
                bulkDeleteBtn.style.display = 'flex';
                bulkDeleteBtn.style.animation = 'fadeInScale 0.3s ease-out forwards';
            } else {
                bulkDeleteBtn.style.display = 'none';
            }
        }
    }
});

// Animate numbers counting up
function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number[data-target]');

    const animateNumber = (element) => {
        const target = parseInt(element.getAttribute('data-target'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    };

    // Intersection Observer for number animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateNumber(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });

    statNumbers.forEach(number => {
        observer.observe(number);
    });
}

// Initialize modern features
function initializeModernFeatures() {
    // Add hover effects to table rows
    const tableRows = document.querySelectorAll('.table-row');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.15)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.action-menu')) {
            document.querySelectorAll('.action-dropdown').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    });
}

// Toggle advanced filter
function toggleAdvancedFilter() {
    const filterPanel = document.getElementById('advancedFilter');
    if (filterPanel.style.display === 'none' || !filterPanel.style.display) {
        filterPanel.style.display = 'block';
        filterPanel.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
        filterPanel.style.display = 'none';
    }
}

// Toggle action menu
function toggleActionMenu(attemptId) {
    const dropdown = document.getElementById(`actionMenu_${attemptId}`);
    const isVisible = dropdown.classList.contains('show');

    // Close all other dropdowns
    document.querySelectorAll('.action-dropdown').forEach(d => {
        d.classList.remove('show');
    });

    // Toggle current dropdown
    if (!isVisible) {
        dropdown.classList.add('show');
    }
}

// Delete attempt function
function deleteAttempt(attemptId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/scoring-attempts/${attemptId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Bulk delete function
function bulkDelete() {
    const checkedBoxes = document.querySelectorAll('.attempt-checkbox:checked');

    if (checkedBoxes.length === 0) {
        showNotification('Vui lòng chọn ít nhất một bài thi để xóa.', 'warning');
        return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${checkedBoxes.length} bài thi đã chọn không?\n\nHành động này không thể hoàn tác!`)) {
        const bulkDeleteForm = document.getElementById('bulkDeleteForm');
        const bulkDeleteIds = document.getElementById('bulkDeleteIds');

        // Clear existing inputs
        bulkDeleteIds.innerHTML = '';

        // Add selected IDs
        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'attempt_ids[]';
            input.value = checkbox.value;
            bulkDeleteIds.appendChild(input);
        });

        // Add loading state
        bulkDeleteForm.classList.add('loading');
        bulkDeleteForm.submit();
    }
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// View toggle functionality
document.querySelectorAll('.view-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');

        const view = this.getAttribute('data-view');
        // Here you can implement grid view vs table view
        console.log('Switching to view:', view);
    });
});

// Auto-submit form when date inputs change
document.addEventListener('change', function(e) {
    if (e.target.matches('input[type="date"]')) {
        const form = e.target.closest('form');
        const dateFrom = form.querySelector('input[name="date_from"]');
        const dateTo = form.querySelector('input[name="date_to"]');

        if (dateFrom && dateTo && dateFrom.value && dateTo.value) {
            form.submit();
        }
    }
});

// Add notification styles
const notificationStyles = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
        z-index: 10000;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
    }

    .notification-success {
        border-left: 4px solid #48bb78;
    }

    .notification-warning {
        border-left: 4px solid #ed8936;
    }

    .notification-info {
        border-left: 4px solid #4299e1;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: #2d3748;
    }

    .notification-close {
        background: none;
        border: none;
        color: #a0aec0;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .notification-close:hover {
        background: #f7fafc;
        color: #4a5568;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;

// Inject notification styles
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);
</script>
@endsection
